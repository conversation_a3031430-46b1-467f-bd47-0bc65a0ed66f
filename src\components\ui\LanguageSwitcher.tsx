'use client'

import { useRouter, usePathname } from 'next/navigation'
import { motion } from 'motion/react'
import { localeNames } from '@/lib/i18n/config'
import { useTranslation } from '@/lib/i18n/client'
import Icon from '@/components/ui/Icon'

export default function LanguageSwitcher() {
  const { locale: currentLocale } = useTranslation()
  const router = useRouter()
  const pathname = usePathname()

  const handleLanguageToggle = () => {
    // 切换到另一种语言
    const nextLocale = currentLocale === 'zh-CN' ? 'en-US' : 'zh-CN'

    // Remove current locale from pathname and add new locale
    const segments = pathname.split('/')
    segments[1] = nextLocale // Replace the locale segment
    const newPath = segments.join('/')

    router.push(newPath)
  }

  // 获取当前语言的显示文本
  const currentLanguageText = localeNames[currentLocale]

  return (
    <motion.button
      onClick={handleLanguageToggle}
      className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-white cursor-pointer"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      aria-label={`切换语言到 ${currentLocale === 'zh-CN' ? 'English' : '中文'}`}
    >
      <Icon name="qiehuan" size="sm" />
      <span className="sm:inline">
        {currentLanguageText}
      </span>
    </motion.button>
  )
}
