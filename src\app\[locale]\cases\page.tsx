/**
 * 用户案例页面 - 服务端组件（仅负责数据获取）
 */
import { getPage } from '@/lib/cms/page'
import type { Metadata } from 'next'
import CasesContent from './CasesContent'

// 启用 ISR - 5分钟重新验证
export const revalidate = 300

interface JourneyPageProps {
  params: Promise<{ locale: string }>
}

// 生成页面元数据
export async function generateMetadata({ params }: JourneyPageProps): Promise<Metadata> {
  const { locale } = await params
  const pageData = await getPage(locale, 'cases')
  if (pageData?.title) {
    return {
      title: pageData.title,
      description: pageData.heroes[0]?.description || pageData.title,
    }
  }

  // 回退到默认标题
  return {
    title: '用户案例',
    description: '探索 Lumii 的用户案例',
  }
}

// 服务端组件 - 只负责数据获取
export default async function Cases({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params
  const pageData = await getPage(locale, 'cases')

  // 将数据传递给客户端组件
  return <CasesContent pageData={pageData} />
}
