/**
 * Strapi CMS API 类型定义
 */

// 基础Strapi实体类型
export interface StrapiEntity {
  id: number
  documentId: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
}

// Strapi API响应的元数据
export interface StrapiMeta {
  pagination: {
    page: number
    pageSize: number
    pageCount: number
    total: number
  }
}

// 通用Strapi API响应结构
export interface StrapiResponse<T> {
  data: T[]
  meta: StrapiMeta
}

// 单个实体的Strapi响应
export interface StrapiSingleResponse<T> {
  data: T
  meta: Record<string, unknown>
}

// Strapi 图片数据结构
export interface StrapiImageFormat {
  ext: string
  url: string
  hash: string
  mime: string
  name: string
  path: string | null
  size: number
  width: number
  height: number
  sizeInBytes: number
}

export interface StrapiImage {
  id: number
  documentId: string
  name: string
  alternativeText: string | null
  caption: string | null
  width: number
  height: number
  formats: {
    large?: StrapiImageFormat
    medium?: StrapiImageFormat
    small?: StrapiImageFormat
    thumbnail?: StrapiImageFormat
  } | null
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl: string | null
  provider: string
  provider_metadata: Record<string, unknown> | null
  createdAt: string
  updatedAt: string
}

// Hero 数据结构
export interface StrapiHero extends StrapiEntity {
  title: string | null
  subtitle: string | null
  description: string | null
  media: StrapiImage | null  // 注意：字段名是 media 不是 image
  buttonText: string | null
  buttonUrl: string | null
}

// 基础块类型
export interface StrapiBlock {
  id: number
  title: string
  description: string | null
  subtitle?: string | null
  blocks?: StrapiBlock[] // 支持嵌套块结构
}



// Header
export interface IHeaderItem {
  id: number;
  label: string
  url: string |  null
}
export interface IHeader {
  id: number
  menus: IHeaderItem[]
}

// Footer按钮数据结构
export interface IFooterButton {
  id: number
  name: string
  label: string
  url: string
  variant?: string
}

// Footer菜单数据结构
export interface IFooterMenu {
  id: number
  label: string
  url: string | null
  menus: Array<{
    id?: number
    label: string
    url?: string
  }>
}

// Footer二维码数据结构
export interface IFooterQRCode {
  id: number
  name: string
  label: string
  image: StrapiImage
  description?: string
}

// Footer社交媒体数据结构
export interface IFooterSocial {
  id: number
  name: string
  label: string
  url: string
  icon: StrapiImage
}

// Footer 数据结构
export interface IFooter {
  id: number
  buttons: IFooterButton[]
  menus: IFooterMenu[]
  qrCodes: IFooterQRCode[]
  socials: IFooterSocial[]
}

// Site数据结构
export interface ISite {
  id: number
  name: string
  title: string
  description: string
  url: string
  logo: StrapiImage | null
  slogan: string
  legal: string
  phone: string
  email: string
}

// PAGE数据结构
export interface IPage {
  id: number
  name: string
  title: string
  blocks: StrapiBlock[]
  heroes: StrapiHero[]
}

// 嵌套过滤器类型
export type StrapiFilterValue = string | number | boolean | (string | number | boolean)[]

export interface StrapiFilterOperators {
  $eq?: string | number | boolean
  $ne?: string | number | boolean
  $in?: (string | number | boolean)[]
  $notIn?: (string | number | boolean)[]
  $lt?: number
  $lte?: number
  $gt?: number
  $gte?: number
  $contains?: string
  $notContains?: string
  $containsi?: string
  $notContainsi?: string
  $startsWith?: string
  $endsWith?: string
  $null?: boolean
  $notNull?: boolean
}

export type StrapiFilter = StrapiFilterValue | StrapiFilterOperators | {
  [key: string]: StrapiFilter
}

// API查询参数
export interface StrapiQueryParams {
  locale?: string
  populate?: string | string[] | Record<string, unknown>
  filters?: Record<string, StrapiFilter>
  sort?: string | string[]
  pagination?: {
    page?: number
    pageSize?: number
    start?: number
    limit?: number
  }
  publicationState?: 'live' | 'preview'
}

// API错误响应
export interface StrapiError {
  status: number
  name: string
  message: string
  details?: Record<string, unknown>
}

export interface StrapiErrorResponse {
  data: null
  error: StrapiError
}

// 语言映射类型
export type StrapiLocale = 'en' | 'zh-CN'

// Next.js locale到Strapi locale的映射
export const LOCALE_MAP: Record<string, StrapiLocale> = {
  'en-US': 'en',
  'zh-CN': 'zh-CN'
} as const
