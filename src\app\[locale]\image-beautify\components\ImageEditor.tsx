/**
 * 图片编辑器主组件 - 有机体组件
 */

'use client'

import { useState, useCallback, useEffect, useRef } from 'react'
import { motion } from 'motion/react'
import { toast } from 'sonner'
import { useTranslation } from '@/lib/i18n/client'
import { useImageBeautifyStore, useOriginalImage, useProcessedImage, useImageBeautifyStatus } from '@/lib/stores/image-beautify'
import { useHeaderHeight } from '@/lib/contexts/HeaderHeightContext'
import FileUpload from '@/components/ui/FileUpload'
import MagicLoadingOverlay from '@/components/ui/MagicLoadingOverlay'
import ControlPanel from './ControlPanel'
import TopActionBar from './TopActionBar'
import ImageCanvas from './ImageCanvas'
import { Compare } from '@/components/ui/compare'
import UserInfoModal from './UserInfoModal'
import DownloadSuccessModal from './DownloadSuccessModal'
import type { ImageBeautifyProps } from '@/types/image-beautify'
import { createRetention } from '@/lib/api/image-beautify'

const topActionBarHeight = 56;
const controlPanelHeight = 112;

// 计算图片在可用空间中的尺寸（保持宽高比，contain效果）
function calculateImageDimensions(
  imageWidth: number,
  imageHeight: number,
  containerWidth: number,
  containerHeight: number,
  topOffset: number,
  bottomOffset: number
): { width: number; height: number; centerTop: number } {
  // 计算实际可用区域（除去头部和底部偏移）
  const availableWidth = containerWidth
  const availableHeight = containerHeight - topOffset - bottomOffset

  // 计算缩放比例，实现contain效果
  const scaleX = availableWidth / imageWidth
  const scaleY = availableHeight / imageHeight
  const scale = Math.min(scaleX, scaleY)

  return {
    width: imageWidth * scale,
    height: imageHeight * scale,
    centerTop: topOffset + availableHeight / 2
  }
}

export default function ImageEditor({ className = '' }: ImageBeautifyProps) {
  const { t } = useTranslation()
  const { headerHeight } = useHeaderHeight()

  const [showUpload, setShowUpload] = useState(true)
  // 底部“牙齿美白”选项显示/隐藏

  // 弹窗状态管理
  const [showUserInfoModal, setShowUserInfoModal] = useState(false)
  const [showDownloadSuccessModal, setShowDownloadSuccessModal] = useState(false)
  const [isSubmittingUserInfo, setIsSubmittingUserInfo] = useState(false)
  const [isDownloadingImage, setIsDownloadingImage] = useState(false)

  // 图片对比模式
  const [isCompareMode, setIsCompareMode] = useState(false)
  // 图片尺寸状态
  const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number; centerTop: number } | null>(null)
  const compareContainerRef = useRef<HTMLDivElement>(null)

  // 使用 Zustand Store
  const originalImage = useOriginalImage()
  const processedImage = useProcessedImage()
  const status = useImageBeautifyStatus()
  const {
    uploadImageFile,
    teethWhitening,
    beautyEnhancement,
    downloadProcessedImageFile,
    reset: resetState,
    error
  } = useImageBeautifyStore()

  // 错误处理
  useEffect(() => {
    if (error) {
      toast.error(error)
    }
  }, [error, t])



  // 处理文件上传
  const handleFileUpload = useCallback(async (file: File) => {
      await uploadImageFile(file)
      setShowUpload(false)
  }, [uploadImageFile])

  // 计算对比图片的尺寸 - 支持传参，避免状态时序问题
  const calculateCompareImageSize = useCallback((forceCompareMode?: boolean) => {
      const shouldCalculate = forceCompareMode ?? isCompareMode

      if (!shouldCalculate || !originalImage) {
        setImageDimensions(null)
        return
      }

      // 使用视口尺寸
      const containerWidth = window.innerWidth
      const containerHeight = window.innerHeight

      // 计算顶部和底部偏移量
      const topOffset = headerHeight + topActionBarHeight // header + top action bar
      const bottomOffset = controlPanelHeight // control panel height

      const dimensions = calculateImageDimensions(
        originalImage.width,
        originalImage.height,
        containerWidth,
        containerHeight,
        topOffset,
        bottomOffset
      )

      // 计算在剩余空间内的居中位置
      const availableHeight = containerHeight - topOffset - bottomOffset
      const centerTop = topOffset + availableHeight / 2

      setImageDimensions({
        ...dimensions,
      centerTop
      })
  }, [isCompareMode, originalImage, headerHeight])

  // 使用 ResizeObserver 监听视口变化 - 更精确的尺寸监听
  useEffect(() => {
    if (!isCompareMode || !originalImage) return

    let timeoutId: NodeJS.Timeout

    const resizeObserver = new ResizeObserver(() => {
      // 防抖处理，避免频繁计算
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        calculateCompareImageSize()
      }, 16) // ~60fps
    })

    // 监听 document.documentElement 来捕获视口变化
    resizeObserver.observe(document.documentElement)

    return () => {
      clearTimeout(timeoutId)
      resizeObserver.disconnect()
    }
  }, [isCompareMode, originalImage, calculateCompareImageSize])

  // 点击“牙齿美白”仅切换选项显示/隐藏
  const handleTeethWhitening = useCallback(async () => {
    await teethWhitening()
  }, [teethWhitening])



  // 处理一键美颜
  const handleBeautyEnhancement = useCallback(async () => {
    await beautyEnhancement()
  }, [beautyEnhancement])

  // 处理用户信息提交
  const handleUserInfoSubmit = useCallback(
    async (userInfo: { name: string; phone: string; city: string; verifyCode: string }) => {
      setIsSubmittingUserInfo(true)

      let mediaData = ''

      try {
        // 获取图片URL作为mediaData
        if (processedImage?.originalUrl) {
          // 如果有处理后的图片，使用处理后图片的原始API URL
          mediaData = processedImage.originalUrl
        } else if (originalImage) {
          // 如果没有处理后的图片，需要先将原图上传到OSS
          const { uploadImage } = await import('@/lib/api/image-beautify')
          const uploadedUrl = await uploadImage(originalImage.file)
          if (uploadedUrl) {
            mediaData = uploadedUrl
          } else {
            toast.error('图片上传失败，请重试')
            return
          }
        } else {
          toast.error('没有可用的图片')
          return
        }

        const params = {
          ...userInfo,
          mediaData,
          source: 'pc',
        }

        const res = await createRetention(params)

        if (res && (res.code === '200' || res.code === '10000')) {
          // 关闭用户信息弹窗，显示成功弹窗
          setShowUserInfoModal(false)
          setShowDownloadSuccessModal(true)
        } else {
          toast.error(res?.content || '提交失败，请重试')
        }
      } catch {
        toast.error('提交失败，请重试')
      } finally {
        setIsSubmittingUserInfo(false)
      }
    },
    [processedImage, originalImage]
  )

  // 实际下载图片
  const handleActualDownload = useCallback(async () => {
    setIsDownloadingImage(true)
    try {
      // 准备小程序翻译文本
      const miniprogramTranslations = {
        saveImageTip: t('imageEditor.miniprogram.saveImageTip', '长按图片保存到相册'),
      close: t('imageEditor.miniprogram.close', '关闭')
      }

      await downloadProcessedImageFile(miniprogramTranslations)
      toast.success(t('imageEditor.download.success', '下载成功'))

      // 关闭成功弹窗
      setShowDownloadSuccessModal(false)
    } catch {
      toast.error('下载失败，请重试')
    } finally {
      setIsDownloadingImage(false)
    }
  }, [downloadProcessedImageFile, t])

  // 处理下载 - 显示用户信息收集弹窗
  const handleDownload = useCallback(() => {
    setShowUserInfoModal(true)
  }, [])

  // 重置编辑器
  const handleReset = useCallback(() => {
    resetState()
    setShowUpload(true)
    setIsCompareMode(false) // 重置时退出对比模式
  }, [resetState])

  // 切换对比模式 - 直接在切换时计算尺寸
  const handleToggleCompare = useCallback(() => {
    setIsCompareMode(prev => {
      const newMode = !prev
      // 进入比较模式时立即计算尺寸，传入新状态避免时序问题
      if (newMode && originalImage) {
        calculateCompareImageSize(true) // 强制计算，不依赖状态
      }
      return newMode
    })
  }, [originalImage, calculateCompareImageSize])

  return (
    <div className={`relative w-full h-full bg-black ${className}`}>
      {showUpload ? (
        /* 上传区域 */
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="h-full flex items-center justify-center"
        >
          <div className="w-full max-w-2xl p-4">
            <FileUpload
              onFileSelect={handleFileUpload}
              disabled={status === 'uploading'}
              className="w-full"
              instructionText={t('imageEditor.upload.instruction', '请上传正面足光照片；保持微笑，露出牙齿')}
              buttonText={t('imageEditor.upload.selectFile', '上传照片')}
            />
          </div>
        </motion.div>
      ) : (
        <>
          {/* 图片显示区域 - 根据模式显示画布或对比组件 */}
          {isCompareMode ? (
            <div
              ref={compareContainerRef}
              className="fixed inset-0 bg-black"
            >
              {imageDimensions && (
                <div
                  style={{
                    position: 'absolute',
                    width: imageDimensions.width,
                    height: imageDimensions.height,
                    left: '50%',
                    top: `${imageDimensions.centerTop}px`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <Compare
                    firstImage={originalImage?.url}
                    secondImage={processedImage?.url || originalImage?.url}
                    className="w-full h-full overflow-hidden"
                    slideMode="hover"
                    autoplay={false}
                  />
                </div>
              )}
            </div>
          ) : (
            <ImageCanvas
              imageUrl={processedImage?.url || originalImage?.url}
              className="w-full h-full"
              topOffset={headerHeight + topActionBarHeight} // header高度 + TopActionBar高度
              bottomOffset={controlPanelHeight} // 提高底部偏移，避免被底部控制面板遮挡
              enableZoom={true} // 启用缩放（支持鼠标滚轮、触摸板、移动端双指）
              enablePanning={true} // 启用平移（支持鼠标拖拽、触摸板、移动端双指）
              enableReset={true} // 启用双击重置
            />
          )}

          {/* 顶部操作栏 - 绝对定位 */}
          <div className="absolute top-0 left-0 right-0 z-10">
            <TopActionBar
              onReset={handleReset}
              onSave={handleDownload}
              hasProcessedImage={!!processedImage}
              top={headerHeight}
            />
          </div>

          {/* 控制面板 - 绝对定位在底部 */}
          <div className="fixed bottom-0 left-0 right-0 z-10">
            <ControlPanel
              onTeethWhitening={handleTeethWhitening}
              onBeautyEnhancement={handleBeautyEnhancement}
              processingStatus={status}
              hasImage={!!originalImage}
              onToggleCompare={handleToggleCompare}
              isCompareMode={isCompareMode}
              showCompareButton={!!processedImage} // 只有当有处理后图片时才显示对比按钮
            />
          </div>
        </>
      )}

      {/* 加载遮罩 */}
      {status === 'processing' && (
        <MagicLoadingOverlay starCount={10} dispersion={0.7} speed={2} sizeScale={0.3} />
      )}

      {/* 用户信息收集弹窗 */}
      <UserInfoModal
        isOpen={showUserInfoModal}
        onClose={() => setShowUserInfoModal(false)}
        onSubmit={handleUserInfoSubmit}
        isSubmitting={isSubmittingUserInfo}
      />

      {/* 下载成功弹窗 */}
      <DownloadSuccessModal
        isOpen={showDownloadSuccessModal}
        onClose={() => setShowDownloadSuccessModal(false)}
        onDownload={handleActualDownload}
        isDownloading={isDownloadingImage}
      />
    </div>
  )
}
