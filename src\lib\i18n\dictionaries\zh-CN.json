{"common": {"loading": "加载中...", "error": "出错了", "retry": "重试", "cancel": "取消", "confirm": "确认", "save": "保存", "edit": "编辑", "delete": "删除", "back": "返回", "next": "下一步", "previous": "上一步", "close": "关闭"}, "navigation": {"home": "首页", "products": "产品服务", "solutions": "解决方案", "imageBeautify": "图片美化", "news": "新闻动态", "contact": "联系我们", "language": "语言", "getStarted": "开始体验"}, "hero": {"title": {"line1": "创新驱动", "line2": "数字化未来"}, "subtitle": "Lumii 致力于为企业提供前沿的数字化解决方案，通过创新技术推动业务增长，引领行业数字化转型的新时代。", "cta": {"primary": "立即开始", "secondary": "了解更多"}, "stats": {"clients": "企业客户", "uptime": "服务可用性", "support": "技术支持"}}, "footer": {"company": "公司信息", "products": "产品服务", "support": "客户支持", "legal": "法律信息", "copyright": "版权所有", "allRightsReserved": "保留所有权利", "links": {"coreProducts": "核心产品", "enterprise": "企业版", "developerTools": "开发者工具", "apiDocs": "API 文档", "solutions": "解决方案", "enterpriseDigital": "企业数字化", "smartManufacturing": "智能制造", "fintech": "金融科技", "edtech": "教育科技", "helpCenter": "帮助中心", "documentation": "技术文档", "community": "社区论坛", "contactSupport": "联系支持", "careers": "招聘信息", "investors": "投资者关系", "privacyPolicy": "隐私政策", "termsOfService": "服务条款", "cookiePolicy": "<PERSON><PERSON> 政策"}, "description": "致力于为企业提供创新的数字化解决方案，推动业务增长和数字化转型。"}, "metadata": {"title": "Lumii - 创新驱动数字化未来", "description": "Lumii 致力于为企业提供前沿的数字化解决方案，通过创新技术推动业务增长，引领行业数字化转型的新时代。", "keywords": "数字化转型, 企业解决方案, 创新技术, 业务增长"}, "imageEditor": {"title": "图片美化", "subtitle": "AI 智能图片处理", "upload": {"title": "上传图片", "description": "选择要处理的图片文件", "dragText": "拖拽图片到此处，或点击选择文件", "instruction": "请上传正面足光照片；保持微笑，露出牙齿", "supportedFormats": "支持 JPG、PNG、WebP 格式", "maxSize": "最大文件大小：10MB", "selectFile": "上传照片", "uploading": "上传中...", "uploadSuccess": "上传成功", "uploadError": "上传失败"}, "processing": {"teethWhitening": "牙齿美白", "teethWhiteningApplied": "已美白", "beautyEnhancement": "一键美颜", "beautyEnhancementApplied": "已美颜", "processing": "处理中...", "completed": "处理完成", "failed": "处理失败"}, "canvas": {"dragToMove": "拖拽移动图片", "scrollToZoom": "滚轮缩放图片", "doubleClickReset": "双击重置位置"}, "download": {"title": "下载图片", "downloading": "下载中...", "success": "图片下载成功", "error": "下载失败"}, "compare": {"enter": "进入对比模式", "exit": "退出对比模式"}, "errors": {"unsupportedFormat": "不支持的文件格式", "fileTooLarge": "文件大小超出限制", "uploadFailed": "文件上传失败", "processingFailed": "图片处理失败", "downloadFailed": "图片下载失败", "canvasError": "画布初始化失败", "networkError": "网络连接错误", "serverError": "服务器错误"}, "actions": {"save": "保存", "reupload": "重新上传", "retry": "重试", "reset": "重置", "cancel": "取消"}, "miniprogram": {"saveImageTip": "长按图片保存到相册", "close": "关闭"}}, "retention": {"title": "免费AI变美", "subTitle": "提交后即可下载", "userName": "姓名", "userNamePlaceholder": "输入姓名", "city": "城市", "cityPlaceholder": "输入城市", "phone": "手机号码", "phonePlaceholder": "输入手机号码", "verificationCode": "验证码", "verificationCodePlaceholder": "输入验证码", "sendCode": "获取验证码", "submit": "提交", "cancel": "取消", "success": {"title": "已提交", "message": "您已填写信息，免费下载图片", "downloadButton": "下载图片", "downloading": "下载中..."}, "sendCodeFailed": "验证码发送失败", "errors": {"nameRequired": "请输入姓名", "nameTooLong": "姓名长度不能超过20个字符", "cityRequired": "请输入城市", "cityTooLong": "城市名称长度不能超过10个字符", "phoneRequired": "请输入手机号码", "phoneInvalid": "请输入正确的手机号码", "verificationCodeRequired": "请输入验证码"}}}